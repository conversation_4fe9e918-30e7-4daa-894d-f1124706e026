/**
 * @file images-stamp.js - 印章图片资源管理模块
 * @description 管理各公司的印章图片base64数据和相关操作
 * @version 1.0.0
 * <AUTHOR> Receipt Generator
 */

/**
 * 印章图片管理器
 * @class StampImageManager - 管理公司印章图片的base64数据
 */
class StampImageManager {
    /**
     * 构造函数
     * @function constructor - 初始化印章图片管理器
     */
    constructor() {
        this.stamps = {
            'sky-mirror': '', // Sky Mirror 印章占位符
            'gomyhire': ''    // GoMyHire 印章占位符
        };
        
        // 推荐的印章图片尺寸
        this.recommendedSize = {
            width: 120,
            height: 120,
            description: '建议尺寸: 120x120px / Recommended: 120x120px'
        };
        
        // 印章透明度设置
        this.stampOpacity = 0.6; // 60%透明度，避免遮盖总金额
        
        // 初始化时加载默认数据
        this.initializeDefaultStamps();
    }

    /**
     * 初始化默认印章数据
     * @function initializeDefaultStamps - 加载默认的印章图片数据
     */
    initializeDefaultStamps() {
        // 这里可以添加默认的base64图片数据
        // 目前保持为空，等待用户上传或设置
        console.log('📋 印章图片管理器已初始化');
    }

    /**
     * 获取指定公司的印章图片
     * @function getStamp - 获取公司印章图片base64数据
     * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
     * @returns {string} 印章图片的base64数据，如果不存在则返回空字符串
     */
    getStamp(company) {
        if (!company || typeof company !== 'string') {
            console.warn('⚠️ 无效的公司代码:', company);
            return '';
        }

        const stamp = this.stamps[company.toLowerCase()];
        if (!stamp) {
            console.log(`📋 公司 ${company} 的印章图片未设置`);
            return '';
        }

        return stamp;
    }

    /**
     * 设置指定公司的印章图片
     * @function setStamp - 设置公司印章图片base64数据
     * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
     * @param {string} base64Data - 印章图片的base64数据
     * @returns {boolean} 设置是否成功
     */
    setStamp(company, base64Data) {
        if (!company || typeof company !== 'string') {
            console.error('❌ 无效的公司代码:', company);
            return false;
        }

        if (!base64Data || typeof base64Data !== 'string') {
            console.error('❌ 无效的base64数据');
            return false;
        }

        // 验证base64数据格式
        if (!this.validateBase64Image(base64Data)) {
            console.error('❌ 无效的图片base64格式');
            return false;
        }

        this.stamps[company.toLowerCase()] = base64Data;
        console.log(`✅ 已设置公司 ${company} 的印章图片`);
        
        // 触发更新事件（如果需要）
        this.notifyStampUpdate(company);
        
        return true;
    }

    /**
     * 删除指定公司的印章图片
     * @function removeStamp - 删除公司印章图片
     * @param {string} company - 公司代码
     * @returns {boolean} 删除是否成功
     */
    removeStamp(company) {
        if (!company || typeof company !== 'string') {
            console.error('❌ 无效的公司代码:', company);
            return false;
        }

        if (this.stamps.hasOwnProperty(company.toLowerCase())) {
            this.stamps[company.toLowerCase()] = '';
            console.log(`✅ 已删除公司 ${company} 的印章图片`);
            this.notifyStampUpdate(company);
            return true;
        }

        console.warn(`⚠️ 公司 ${company} 的印章图片不存在`);
        return false;
    }

    /**
     * 获取所有公司的印章图片列表
     * @function getAllStamps - 获取所有印章图片数据
     * @returns {Object} 包含所有公司印章图片的对象
     */
    getAllStamps() {
        return { ...this.stamps };
    }

    /**
     * 检查指定公司是否有印章图片
     * @function hasStamp - 检查公司是否设置了印章图片
     * @param {string} company - 公司代码
     * @returns {boolean} 是否存在印章图片
     */
    hasStamp(company) {
        if (!company || typeof company !== 'string') {
            return false;
        }
        
        const stamp = this.stamps[company.toLowerCase()];
        return stamp && stamp.length > 0;
    }

    /**
     * 获取推荐的印章图片尺寸
     * @function getRecommendedSize - 获取推荐的印章图片尺寸信息
     * @returns {Object} 包含推荐尺寸的对象
     */
    getRecommendedSize() {
        return { ...this.recommendedSize };
    }

    /**
     * 获取印章透明度设置
     * @function getStampOpacity - 获取印章透明度值
     * @returns {number} 透明度值 (0-1)
     */
    getStampOpacity() {
        return this.stampOpacity;
    }

    /**
     * 设置印章透明度
     * @function setStampOpacity - 设置印章透明度值
     * @param {number} opacity - 透明度值 (0-1)
     * @returns {boolean} 设置是否成功
     */
    setStampOpacity(opacity) {
        if (typeof opacity !== 'number' || opacity < 0 || opacity > 1) {
            console.error('❌ 无效的透明度值，应为0-1之间的数字');
            return false;
        }

        this.stampOpacity = opacity;
        console.log(`✅ 印章透明度已设置为 ${opacity}`);
        return true;
    }

    /**
     * 验证base64图片数据格式
     * @function validateBase64Image - 验证base64图片数据是否有效
     * @param {string} base64Data - base64数据
     * @returns {boolean} 是否为有效的图片base64数据
     */
    validateBase64Image(base64Data) {
        if (!base64Data || typeof base64Data !== 'string') {
            return false;
        }

        // 检查是否包含data:image前缀
        const imageRegex = /^data:image\/(jpeg|jpg|png|gif|webp|svg\+xml);base64,/i;
        return imageRegex.test(base64Data);
    }

    /**
     * 通知印章图片更新
     * @function notifyStampUpdate - 通知系统印章图片已更新
     * @param {string} company - 更新的公司代码
     */
    notifyStampUpdate(company) {
        // 触发自定义事件，通知其他组件印章图片已更新
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const event = new CustomEvent('stampUpdated', {
                detail: { company: company }
            });
            window.dispatchEvent(event);
        }
    }

    /**
     * 导出印章图片数据为JSON
     * @function exportToJSON - 导出所有印章图片数据
     * @returns {string} JSON格式的印章图片数据
     */
    exportToJSON() {
        return JSON.stringify({
            stamps: this.stamps,
            opacity: this.stampOpacity
        }, null, 2);
    }

    /**
     * 从JSON导入印章图片数据
     * @function importFromJSON - 从JSON数据导入印章图片
     * @param {string} jsonData - JSON格式的印章图片数据
     * @returns {boolean} 导入是否成功
     */
    importFromJSON(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            if (typeof data === 'object' && data !== null) {
                if (data.stamps) {
                    this.stamps = { ...this.stamps, ...data.stamps };
                }
                if (typeof data.opacity === 'number') {
                    this.stampOpacity = data.opacity;
                }
                console.log('✅ 印章图片数据导入成功');
                return true;
            }
        } catch (error) {
            console.error('❌ 印章图片数据导入失败:', error);
        }
        return false;
    }

    /**
     * 检查图片尺寸是否符合推荐标准
     * @function checkImageSize - 检查图片尺寸
     * @param {HTMLImageElement} img - 图片元素
     * @returns {Object} 尺寸检查结果
     */
    checkImageSize(img) {
        const result = {
            isRecommended: false,
            actualSize: { width: img.width, height: img.height },
            recommendedSize: this.recommendedSize,
            message: ''
        };

        if (img.width === this.recommendedSize.width && img.height === this.recommendedSize.height) {
            result.isRecommended = true;
            result.message = '✅ 图片尺寸符合推荐标准';
        } else {
            result.message = `⚠️ 建议使用 ${this.recommendedSize.width}x${this.recommendedSize.height}px 尺寸`;
        }

        return result;
    }
}

// 创建全局实例
const stampImageManager = new StampImageManager();

// 导出模块（支持不同的模块系统）
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = { StampImageManager, stampImageManager };
} else if (typeof window !== 'undefined') {
    // 浏览器环境
    window.StampImageManager = StampImageManager;
    window.stampImageManager = stampImageManager;
}

console.log('🎨 印章图片管理模块已加载');
